<!-- 聊天主页 -->
<template>
  <div class="flex h-screen overflow-hidden bg-[#ebeff5] font-sans">
    <!-- 最左侧导航栏 -->
    <NavigationSidebar @nav-change="handleNavChange" />

    <!-- 左侧聊天列表 -->
    <ChatSidebar
      :contacts="chats"
      :current-contact-id="currentChatId"
      :is-loading="isLoadingUsers"
      :loading-text="loadingStatus"
      @select-contact="selectChat"
      @refresh="loadChatContacts"
    />

    <!-- 右侧聊天区域 -->
    <div class="flex-1 flex flex-col min-h-0">
      <!-- 聊天头部 -->
      <ChatHeader
        v-if="currentChat"
        :current-contact="currentChat"
        @more-options="showMoreOptions"
        class="flex-shrink-0"
      />

      <!-- 消息列表 -->
      <MessageList
        ref="messageListRef"
        :messages="currentMessages"
        :current-contact="currentChat"
        class="flex-1 min-h-0"
      />

      <!-- 消息输入 -->
      <MessageInput
        v-if="currentChat"
        :current-contact="currentChat"
        @send-message="sendMessage"
        @insert-emoji="insertEmoji"
        @attach-file="attachFile"
        @insert-image="insertImage"
        class="flex-shrink-0"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, onUnmounted } from 'vue'
import { useUserStore } from '../store/user'
import { useMessageStore } from '../store/message'
import { apiClient } from '../api'
import type { User } from '../api'
import { notificationService, ErrorType, ErrorSeverity } from '../services/notificationService'
import NavigationSidebar from '../components/NavigationSidebar.vue'
import ChatSidebar from '../components/ChatSidebar.vue'
import ChatHeader from '../components/ChatHeader.vue'
import MessageList from '../components/MessageList.vue'
import MessageInput from '../components/MessageInput.vue'

// 用户状态管理
const userStore = useUserStore()
const messageStore = useMessageStore()

// 响应式数据
const currentChatId = ref<string | null>(null)
const isLoadingUsers = ref(false)
const loadingStatus = ref('')
const messageListRef = ref<InstanceType<typeof MessageList>>()

// 防抖和请求取消相关
let selectChatTimer: NodeJS.Timeout | null = null
let currentAbortController: AbortController | null = null

// 聊天数据 - 从聊天联系人API获取有聊天记录的联系人
const chats = ref<
  Array<{
    id: string
    name: string
    avatar: string
    status: string
    lastMessage: string
    lastMessageTime: Date
    unreadCount: number
    user: User
    hasLastMessage?: boolean // 标记是否有最后消息，用于排序
  }>
>([])

// 从API加载所有用户列表
const loadChatContacts = async () => {
  console.log('🔍 [loadChatContacts] 开始加载联系人列表')
  try {
    // 清空当前选中的联系人
    currentChatId.value = null
    messageStore.setCurrentChatUser('')

    isLoadingUsers.value = true
    loadingStatus.value = '正在获取用户列表...'
    console.log('🔍 [loadChatContacts] 设置加载状态，开始调用API')

    const usersResponse = await apiClient.getUsers()
    console.log('🔍 [loadChatContacts] API响应:', usersResponse)

    if (!usersResponse.success) {
      throw new Error('获取用户列表失败')
    }

    // 过滤掉当前用户，转换为聊天列表格式
    const filteredUsers = usersResponse.users
      .filter((user) => user.id !== userStore.currentUser.value?.id)
      .map((user) => {
        // 现在user已经包含lastMessage属性
        return {
          id: user.id,
          name: user.displayName,
          avatar: getNameAvatar(user.displayName),
          status: user.isOnline ? '在线' : '离线',
          lastMessage: user.lastMessage?.content || '', // 有lastMessage显示内容，没有则为空字符串
          lastMessageTime: user.lastMessage
            ? new Date(user.lastMessage.timestamp)
            : new Date(user.lastOnlineTime), // 有lastMessage用消息时间，没有用最后在线时间
          unreadCount: 0,
          user: user,
          hasLastMessage: !!user.lastMessage // 标记是否有最后消息，用于排序
        }
      })

    // 按照最后消息时间排序：有消息的在前，按时间倒序；没有消息的在后，按最后在线时间倒序
    chats.value = filteredUsers.sort((a, b) => {
      // 如果都有最后消息，按消息时间倒序
      if (a.hasLastMessage && b.hasLastMessage) {
        return b.lastMessageTime.getTime() - a.lastMessageTime.getTime()
      }
      // 如果都没有最后消息，按最后在线时间倒序
      if (!a.hasLastMessage && !b.hasLastMessage) {
        return b.lastMessageTime.getTime() - a.lastMessageTime.getTime()
      }
      // 有消息的排在没消息的前面
      if (a.hasLastMessage && !b.hasLastMessage) {
        return -1
      }
      if (!a.hasLastMessage && b.hasLastMessage) {
        return 1
      }
      return 0
    })

    console.log('加载用户列表成功:', chats.value)
  } catch (error) {
    console.error('加载用户列表失败:', error)
    loadingStatus.value = '加载失败'

    // 显示错误提示
    notificationService.handleError({
      type: ErrorType.API,
      severity: ErrorSeverity.MEDIUM,
      message: '加载联系人列表失败',
      details: error instanceof Error ? error.message : '未知错误',
      retryable: true,
      action: loadChatContacts,
      actionText: '重新加载'
    })

    // 如果API失败，使用默认数据
    chats.value = [
      {
        id: '689da0e4467567db78ae889d',
        name: '陈颢文',
        avatar: getNameAvatar('陈颢文'),
        status: '在线',
        lastMessage: '点击开始聊天',
        lastMessageTime: new Date(Date.now() - 1000 * 60 * 5),
        unreadCount: 0,
        user: {
          id: '689da0e4467567db78ae889d',
          username: 'chenhaowen',
          email: '<EMAIL>',
          displayName: '陈颢文',
          avatar: '/avatars/default.png',
          isOnline: true,
          lastOnlineTime: '2025-08-14T08:42:27.539Z'
        }
      }
    ]
  } finally {
    isLoadingUsers.value = false
    // 清空加载状态
    setTimeout(() => {
      loadingStatus.value = ''
    }, 1000)
  }
}

interface Message {
  id: string
  senderId: string
  senderName: string
  content: string
  timestamp: Date
}

// 计算属性
const currentChat = computed(() => {
  const chat = chats.value.find((chat) => chat.id === currentChatId.value)
  console.log(`当前聊天联系人:`, chat)
  console.log(`当前聊天ID: ${currentChatId.value}`)
  console.log(
    `联系人列表:`,
    chats.value.map((c) => ({ id: c.id, name: c.name }))
  )
  return chat || null
})

// 直接使用messageStore的currentMessages，并转换格式
const currentMessages = computed(() => {
  const storeMessages = messageStore.currentMessages

  console.log(`🔍 [Chat.vue] 当前聊天用户ID: ${messageStore.currentChatUserId}`)
  console.log(`🔍 [Chat.vue] 原始消息数量: ${storeMessages.length}`)
  console.log(`🔍 [Chat.vue] 原始消息:`, storeMessages)

  // 转换为组件需要的格式
  const transformedMessages = storeMessages.map((msg) => ({
    id: msg.id,
    senderId: msg.senderId,
    senderName: getSenderName(msg.senderId),
    content: msg.content,
    timestamp: new Date(msg.timestamp),
    isSending: msg.isSending,
    sendError: msg.sendError
  }))

  console.log(`🔍 [Chat.vue] 转换后消息数量: ${transformedMessages.length}`)
  console.log(`🔍 [Chat.vue] 转换后消息:`, transformedMessages)

  return transformedMessages
})

// 获取发送者姓名
const getSenderName = (senderId: string) => {
  const currentUserId = userStore.currentUser.value?.id

  if (senderId === currentUserId) {
    return userStore.userDisplayName.value || '我'
  }

  // 从聊天联系人中查找
  const contact = chats.value.find((chat) => chat.user.id === senderId)
  return contact?.user.displayName || '未知用户'
}

// 处理收到新消息时的联系人列表更新
const handleNewMessage = (senderId: string, content: string, timestamp: number) => {
  // 查找发送者在联系人列表中的位置
  const senderIndex = chats.value.findIndex((chat) => chat.id === senderId)

  if (senderIndex !== -1) {
    const sender = chats.value[senderIndex]

    // 更新最后消息和时间
    sender.lastMessage = content
    sender.lastMessageTime = new Date(timestamp)
    sender.hasLastMessage = true

    // 如果不是当前聊天用户，增加未读计数
    if (currentChatId.value !== senderId) {
      sender.unreadCount = (sender.unreadCount || 0) + 1
    }

    // 将该联系人移到列表顶部
    chats.value.splice(senderIndex, 1)
    chats.value.unshift(sender)

    console.log(`收到来自 ${sender.name} 的新消息，未读数: ${sender.unreadCount}`)
  }
}

// 清除未读计数
const clearUnreadCount = (userId: string) => {
  const contact = chats.value.find((chat) => chat.id === userId)
  if (contact && contact.unreadCount > 0) {
    contact.unreadCount = 0
    console.log(`清除用户 ${contact.name} 的未读计数`)
  }
}

// 同步messageStore中的chatSessions到联系人列表
const syncChatSessionsToContacts = () => {
  const sessions = messageStore.chatSessions

  sessions.forEach((session, userId) => {
    const contactIndex = chats.value.findIndex((chat) => chat.id === userId)

    if (contactIndex !== -1) {
      const contact = chats.value[contactIndex]
      // 更新联系人信息
      if (session.lastMessage) {
        contact.lastMessage = session.lastMessage.content
        contact.lastMessageTime = new Date(session.lastMessage.timestamp)
        contact.hasLastMessage = true
        console.log(`🔍 [Chat.vue] 更新最后消息: ${session.lastMessage.content}`)
      }

      // 更新未读计数（如果不是当前聊天用户）
      const oldUnreadCount = contact.unreadCount
      if (currentChatId.value !== userId) {
        contact.unreadCount = session.unreadCount
      } else {
        contact.unreadCount = 0
      }
      console.log(`🔍 [Chat.vue] 未读计数更新: ${oldUnreadCount} -> ${contact.unreadCount}`)

      // 如果有新消息，将联系人移到顶部
      if (session.unreadCount > 0 && currentChatId.value !== userId) {
        chats.value.splice(contactIndex, 1)
        chats.value.unshift(contact)
        console.log(`🔍 [Chat.vue] ${contact.name} 有新消息，移到列表顶部`)
      }
    } else {
      console.log(`🔍 [Chat.vue] ⚠️ 用户 ${userId} 不在联系人列表中`)
    }
  })

  console.log(
    '🔍 [Chat.vue] 同步完成，当前联系人列表:',
    chats.value.map((c) => ({ name: c.name, unreadCount: c.unreadCount }))
  )
}

// 组件挂载时的初始化
onMounted(async () => {
  console.log('🔍 [Chat.vue] 组件挂载开始')
  console.log('🔍 [Chat.vue] 用户认证状态:', userStore.isAuthenticated.value)
  console.log('🔍 [Chat.vue] 当前用户:', userStore.currentUser.value)

  // 检查用户是否已登录
  if (!userStore.isAuthenticated.value) {
    console.log('❌ [Chat.vue] 用户未登录，应该跳转到登录页')
    notificationService.warning('请先登录')
    return
  }

  console.log('✅ [Chat.vue] 用户已登录，开始初始化')

  // 立即开始WebSocket连接，不等待其他初始化完成
  const initWebSocketPromise = (async () => {
    try {
      console.log('🔗 [Chat.vue] 立即开始WebSocket连接')
      await messageStore.initWebSocket()
      console.log('✅ [Chat.vue] WebSocket连接成功')
    } catch (error) {
      console.error('❌ [Chat.vue] WebSocket连接失败:', error)
      notificationService.handleError({
        type: ErrorType.WEBSOCKET,
        severity: ErrorSeverity.HIGH,
        message: 'WebSocket连接失败',
        details: error instanceof Error ? error.message : '网络连接异常',
        retryable: true,
        action: () => messageStore.initWebSocket(),
        actionText: '重新连接'
      })
    }
  })()

  // 并行加载聊天联系人列表
  const loadContactsPromise = loadChatContacts()

  // 等待两个任务都完成
  await Promise.all([initWebSocketPromise, loadContactsPromise])

  // 使用watch监听messageStore中的chatSessions变化
  watch(
    () => messageStore.chatSessions,
    (newSessions, oldSessions) => {
      syncChatSessionsToContacts()
    },
    { deep: true }
  )
})

// 组件卸载时清理资源
onUnmounted(() => {
  // 清理防抖定时器
  if (selectChatTimer) {
    clearTimeout(selectChatTimer)
    selectChatTimer = null
  }

  // 取消正在进行的请求
  if (currentAbortController) {
    currentAbortController.abort()
    currentAbortController = null
  }

  console.log('Chat组件已卸载，资源已清理')
})

// 工具函数
const getNameAvatar = (name: string) => {
  // 获取姓名的后两个字作为头像
  return name.length >= 2 ? name.slice(-2) : name
}

// 方法
const selectChat = (chatId: string) => {
  console.log(`=== 选择聊天开始 ===`)
  console.log(`选择聊天: ${chatId}`)

  // 检查是否是同一个用户
  const isSameUser = currentChatId.value === chatId
  console.log(`是否是同一个用户: ${isSameUser}`)

  // 清除之前的防抖定时器
  if (selectChatTimer) {
    clearTimeout(selectChatTimer)
    selectChatTimer = null
  }

  // 取消之前的请求
  if (currentAbortController) {
    currentAbortController.abort()
    currentAbortController = null
    console.log('已取消之前的聊天历史加载请求')
  }

  // 立即设置当前聊天ID和UI状态
  currentChatId.value = chatId
  console.log(`设置currentChatId: ${currentChatId.value}`)

  // 设置messageStore的当前聊天用户
  messageStore.setCurrentChatUser(chatId)
  console.log(`设置messageStore currentChatUserId: ${messageStore.currentChatUserId}`)

  // 更新没有lastMessageTime的联系人
  const contact = chats.value.find((c) => c.id === chatId)
  if (contact && !contact.hasLastMessage) {
    contact.lastMessageTime = new Date()
    contact.hasLastMessage = true
    console.log(`更新联系人 ${contact.name} 的lastMessageTime为当前时间`)
  }

  // 清除该联系人的未读计数
  clearUnreadCount(chatId)

  // 检查是否需要加载聊天历史
  const existingMessages = messageStore.messages.get(chatId)
  const hasMessages = existingMessages && existingMessages.length > 0

  console.log(`用户${chatId}已有消息数量: ${existingMessages?.length || 0}`)
  console.log(`是否需要加载历史: ${!isSameUser || !hasMessages}`)

  // 如果是同一个用户且已有消息，不需要重新加载
  if (isSameUser && hasMessages) {
    console.log(`用户${chatId}已选中且有消息数据，跳过历史加载`)
    console.log(`=== 选择聊天结束（跳过加载） ===`)
    return
  }

  // 使用防抖机制加载聊天历史
  selectChatTimer = setTimeout(() => {
    loadChatHistoryWithCancel(chatId)
  }, 400) // 400ms防抖延迟
}

// 带取消功能的聊天历史加载
const loadChatHistoryWithCancel = async (chatId: string) => {
  console.log(`开始加载聊天历史: ${chatId}`)

  // 创建新的AbortController
  currentAbortController = new AbortController()
  const signal = currentAbortController.signal

  try {
    const success = await messageStore.loadChatHistoryWithCancel(chatId, 1, 50, signal)
    console.log(`聊天历史加载结果: ${success}`)

    // 检查请求是否被取消
    if (signal.aborted) {
      console.log('聊天历史加载请求被取消')
      return
    }

    if (!success) {
      // 区分是否是并发冲突导致的失败
      if (messageStore.isLoading) {
        console.log('聊天历史加载失败：并发冲突，忽略错误提示')
      } else {
        notificationService.warning('聊天历史加载失败，请稍后重试')
      }
    }
  } catch (error) {
    // 检查是否是取消错误
    if (error instanceof Error && error.name === 'AbortError') {
      console.log('聊天历史加载请求被主动取消')
      return
    }

    console.error('加载聊天历史失败:', error)
    notificationService.handleError({
      type: ErrorType.API,
      severity: ErrorSeverity.LOW,
      message: '聊天历史加载失败',
      details: error instanceof Error ? error.message : '未知错误',
      retryable: true,
      action: () => loadChatHistoryWithCancel(chatId),
      actionText: '重新加载'
    })
  } finally {
    // 清理AbortController
    if (currentAbortController && !currentAbortController.signal.aborted) {
      currentAbortController = null
    }
  }

  // 检查加载后的消息
  const messages = messageStore.currentMessages
  console.log(`当前消息数量: ${messages.length}`, messages)

  // 检查messages Map中的数据
  console.log(`messages Map中的所有数据:`, Array.from(messageStore.messages.entries()))
  console.log(`=== 选择聊天结束 ===`)
}

const showMoreOptions = () => {
  console.log('显示更多选项')
  // TODO: 实现更多选项功能
}

const handleLogout = () => {
  userStore.logout()
  messageStore.disconnectWebSocket()
  // 触发父组件重新渲染，显示登录页面
  window.location.reload()
}

const insertEmoji = () => {
  console.log('插入表情')
  // TODO: 实现表情选择功能
}

const attachFile = () => {
  console.log('附加文件')
  // TODO: 实现文件上传功能
}

const insertImage = () => {
  console.log('插入图片')
  // TODO: 实现图片上传功能
}

const sendMessage = async (content: string) => {
  if (!content.trim() || !currentChatId.value) {
    notificationService.warning('请输入消息内容')
    return
  }

  try {
    // 使用messageStore发送消息
    const success = await messageStore.sendMessage(currentChatId.value, content)

    if (success) {
      // 更新聊天列表中的最后消息
      const chat = chats.value.find((c) => c.id === currentChatId.value)
      if (chat) {
        chat.lastMessage = content
        chat.lastMessageTime = new Date()
        chat.hasLastMessage = true
      }
      // 显示发送成功提示（可选）
      // notificationService.success('消息发送成功')
    } else {
      // 发送失败，但消息已经添加到列表中，状态会自动更新
      console.log('消息发送失败，但已添加到消息列表中显示失败状态')
    }
  } catch (error) {
    console.error('发送消息异常:', error)
    // 这里处理真正的异常情况（如网络错误等）
    notificationService.handleError({
      type: ErrorType.MESSAGE_SEND,
      severity: ErrorSeverity.MEDIUM,
      message: '消息发送异常',
      details: error instanceof Error ? error.message : '网络连接异常',
      retryable: true,
      action: () => sendMessage(content),
      actionText: '重新发送'
    })
  }
}

// 处理导航变化
const handleNavChange = (itemId: string) => {
  console.log('导航切换到:', itemId)
}
</script>
