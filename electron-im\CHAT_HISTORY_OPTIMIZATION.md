# 聊天历史加载优化 - 防抖和请求取消

## 问题描述

快速切换会话时，会出现"聊天历史加载失败，请稍后重试"的错误提示。

## 问题原因

1. **并发控制问题**：当前一个会话的历史记录还在加载中时，新的加载请求会被直接拒绝
2. **没有防抖机制**：用户每次点击都会立即触发加载请求
3. **API请求冲突**：快速切换时可能有多个API请求同时进行

## 解决方案

### 1. 防抖机制
- 在 `selectChat` 函数中添加 300ms 防抖延迟
- 避免用户快速点击时产生大量无效请求

### 2. 请求取消机制
- 使用 `AbortController` 取消正在进行的请求
- 切换会话时自动取消前一个请求

### 3. 智能错误处理
- 区分真实错误和并发冲突
- 对于并发冲突不显示错误提示

## 代码修改

### Chat.vue 修改

1. **添加防抖和取消变量**：
```typescript
// 防抖和请求取消相关
let selectChatTimer: NodeJS.Timeout | null = null
let currentAbortController: AbortController | null = null
```

2. **重构 selectChat 函数**：
- 立即设置UI状态（currentChatId、清除未读等）
- 使用防抖机制延迟加载历史记录
- 取消之前的请求

3. **新增 loadChatHistoryWithCancel 函数**：
- 支持 AbortSignal 的历史记录加载
- 智能错误处理，区分取消和真实错误

4. **组件卸载清理**：
- 清理防抖定时器
- 取消正在进行的请求

### MessageStore 修改

1. **新增 loadChatHistoryWithCancel 方法**：
- 支持 AbortSignal 参数
- 在关键点检查是否被取消
- 正确处理取消错误

### API Client 修改

1. **新增 getChatHistoryWithCancel 方法**：
- 支持 AbortSignal 参数
- 模拟数据也支持取消机制

2. **改进 request 方法**：
- 支持外部传入的 AbortSignal
- 组合超时控制和外部取消信号
- 正确区分超时和外部取消

## 技术特点

### 防抖机制
- **延迟时间**：300ms
- **触发条件**：用户停止点击后才开始加载
- **用户体验**：减少无效请求，提升响应速度

### 请求取消
- **取消时机**：切换到新会话时
- **取消方式**：AbortController.abort()
- **错误处理**：区分 AbortError 和其他错误

### 状态管理
- **立即响应**：UI状态立即更新
- **延迟加载**：历史记录延迟加载
- **资源清理**：组件卸载时清理资源

## 预期效果

1. ✅ **消除错误提示**：快速切换不再显示加载失败
2. ✅ **提升性能**：减少无效网络请求
3. ✅ **改善体验**：界面响应更流畅
4. ✅ **资源优化**：自动取消无用请求

## 使用说明

### 开发者
- 新的 `loadChatHistoryWithCancel` 方法可用于其他需要取消功能的场景
- 防抖延迟可根据需要调整（当前300ms）

### 用户
- 快速切换会话时不再出现错误提示
- 界面响应更加流畅
- 网络请求更加高效

## 兼容性

- 保持原有 `loadChatHistory` 方法不变
- 新增功能向后兼容
- 不影响现有功能

## 测试建议

1. **快速切换测试**：连续快速点击不同会话
2. **网络延迟测试**：在慢网络环境下测试
3. **并发测试**：同时进行多个操作
4. **错误恢复测试**：网络错误后的恢复能力
